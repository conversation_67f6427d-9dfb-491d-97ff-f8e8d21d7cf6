import 'package:flutter/cupertino.dart';
import 'package:hive/hive.dart';
import 'package:note_x/lib.dart';
import 'dart:math' as math;

class HiveFolderService {
  static Box<FolderModel> get _folderBox => HiveService().folderBox;
  static Box<NoteModel> get _noteBox => HiveService().noteBox;
  static Box<NoteModel> get _noteFailedBox => HiveService().noteFailedBox;

  // Create folder (root or subfolder)
  static Future<void> createFolder({
    required String name,
    required String folderBackendId,
    String? parentFolderId,
  }) async {
    // Validate parent if provided
    if (parentFolderId != null && parentFolderId.isNotEmpty) {
      final parent = _folderBox.get(parentFolderId);
      if (parent == null) {
        throw Exception('Parent folder not found: $parentFolderId');
      }
      if (parent.level >= FolderModel.maxLevel) {
        throw Exception('Cannot create subfolder: Maximum level reached');
      }
    }

    // Create folder
    final folder = FolderModel(
      folderName: name,
      backendId: folderBackendId,
      id: folderBackendId,
      parentFolderId: parentFolderId,
      level: parentFolderId != null ? (_folderBox.get(parentFolderId)?.level ?? 0) + 1 : 1,
      path: _buildPath(name, parentFolderId),
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
    );

    // Save folder
    await _folderBox.put(folderBackendId, folder);

    // Update parent's subfolder list if this is a subfolder
    if (parentFolderId != null && parentFolderId.isNotEmpty) {
      await _addSubfolderToParent(parentFolderId, folderBackendId);
    }
  }

  // Rename folder
  static Future<void> renameFolderById({
    required String folderLocalId,
    required String newName,
  }) async {
    final folder = _folderBox.get(folderLocalId);
    if (folder == null) {
      throw Exception('Folder not found: $folderLocalId');
    }

    // Update folder name and path
    final updatedFolder = folder.copyWith(
      folderName: newName,
      updatedAt: DateTime.now().toIso8601String(),
    );

    // Update path for this folder and all descendants
    await _updateFolderPath(updatedFolder);
    await _folderBox.put(folderLocalId, updatedFolder);

    // Update paths for all descendants
    await _updateDescendantPaths(folderLocalId);
  }

  // Delete folder and handle subfolders/notes
  static Future<void> deleteFolder({
    required String folderLocalId,
    bool deleteNotes = false,
  }) async {
    final folder = _folderBox.get(folderLocalId);
    if (folder == null) return;

    // Get all descendant folders (subfolders, sub-subfolders, etc.)
    final allDescendants = _getAllDescendantFolders(folderLocalId);
    final allFolderIds = [folderLocalId, ...allDescendants.map((f) => f.backendId)];

    // Handle notes in all affected folders
    for (final folderId in allFolderIds) {
      final notesInFolder = _noteBox.values.where((note) => note.folderId == folderId);
      
      for (final note in notesInFolder) {
        if (deleteNotes) {
          await _noteBox.delete(note.id);
        } else {
          // Move notes to root (no folder)
          final updatedNote = note.copyWith(
            folderId: '',
            folderName: '',
          );
          await _noteBox.put(note.id, updatedNote);
        }
      }
    }

    // Remove from parent's subfolder list
    if (folder.parentFolderId != null && folder.parentFolderId!.isNotEmpty) {
      await _removeSubfolderFromParent(folder.parentFolderId!, folderLocalId);
    }

    // Delete all folders (descendants first, then parent)
    for (final descendant in allDescendants.reversed) {
      await _folderBox.delete(descendant.backendId);
    }
    await _folderBox.delete(folderLocalId);
  }

  // Move folder to another folder
  static Future<void> moveFolderToFolder({
    required String folderId,
    required String targetFolderId,
  }) async {
    debugPrint('HiveFolderService: Moving folder $folderId to $targetFolderId');

    final folder = _folderBox.get(folderId);
    final targetFolder = _folderBox.get(targetFolderId);

    if (folder == null) {
      throw Exception('Source folder not found: $folderId');
    }
    if (targetFolder == null) {
      throw Exception('Target folder not found: $targetFolderId');
    }

    debugPrint('HiveFolderService: Source folder: ${folder.folderName} (parent: ${folder.parentFolderId})');
    debugPrint('HiveFolderService: Target folder: ${targetFolder.folderName} (level: ${targetFolder.level})');

    // Prevent moving folder into itself or its descendants
    if (folderId == targetFolderId || _isDescendantOf(targetFolderId, folderId)) {
      throw Exception('Cannot move folder into itself or its descendant');
    }

    // Check level constraints
    if (targetFolder.level >= FolderModel.maxLevel) {
      throw Exception('Target folder cannot have subfolders: Maximum level reached');
    }

    // Remove from old parent
    if (folder.parentFolderId != null && folder.parentFolderId!.isNotEmpty) {
      debugPrint('HiveFolderService: Removing from old parent: ${folder.parentFolderId}');
      await _removeSubfolderFromParent(folder.parentFolderId!, folderId);
    }

    // Update folder's parent and level
    final newLevel = targetFolder.level + 1;
    final updatedFolder = folder.copyWith(
      parentFolderId: targetFolderId,
      level: newLevel,
      updatedAt: DateTime.now().toIso8601String(),
    );

    debugPrint('HiveFolderService: Updated folder - new parent: $targetFolderId, new level: $newLevel');

    // Update paths for moved folder and all its descendants
    await _updateFolderPath(updatedFolder);
    await _folderBox.put(folderId, updatedFolder);
    await _updateDescendantPaths(folderId);

    // Add to new parent
    await _addSubfolderToParent(targetFolderId, folderId);

    debugPrint('HiveFolderService: Move completed successfully');

    // Print current folder structure for debugging
    debugPrint('HiveFolderService: Current folder structure after move:');
    debugPrint(printFolderTree());
  }

  // Add note to folder
  static Future<void> addNoteToFolder({
    required NoteModel note,
    required String folderBackendId,
  }) async {
    final folder = folderBackendId.isNotEmpty ? _folderBox.get(folderBackendId) : null;
    
    final updatedNote = note.copyWith(
      folderId: folderBackendId,
      folderName: folder?.folderName ?? ''
    );
    
    await _noteBox.put(note.id, updatedNote);
  }

  // Get all folders sorted by update date
  static List<FolderModel> getAllFolders() {
    final folders = _folderBox.values.toList();
    
    folders.sort((a, b) {
      final dateA = DateTime.tryParse(a.updatedAt) ?? DateTime(1970);
      final dateB = DateTime.tryParse(b.updatedAt) ?? DateTime(1970);
      return dateB.compareTo(dateA);
    });
    
    return folders;
  }

  // Get root folders only
  static List<FolderModel> getRootFolders() {
    final allFolders = _folderBox.values.toList();
    final rootFolders = allFolders.where((folder) {
      if (folder.parentFolderId == null || folder.parentFolderId!.isEmpty) {
        return true;
      }
      final parent = _folderBox.get(folder.parentFolderId!);
      return parent == null;
    }).toList();
    
    return rootFolders;
  }

  // Get direct subfolders of a folder
  static List<FolderModel> getSubFolders({required String folderBackendId}) {
    return _folderBox.values
        .where((folder) => folder.parentFolderId == folderBackendId)
        .toList()
      ..sort((a, b) => a.folderName.compareTo(b.folderName));
  }

  // Get all descendant folders (recursive)
  static List<FolderModel> getAllDescendantFolders({required String folderBackendId}) {
    return _getAllDescendantFolders(folderBackendId);
  }

  // Get folder by ID
  static FolderModel? getFolderById({required String folderBackendId}) {
    try {
      return _folderBox.get(folderBackendId);
    } catch (e) {
      return null;
    }
  }

  // Get folder name by ID
  static String getNameFolderByBackEndId(String folderBackendId) {
    if (folderBackendId.isEmpty) return '';
    
    final folder = _folderBox.get(folderBackendId);
    return folder?.folderName ?? S.current.add_folder;
  }

  // Get notes in folder
  static Future<List<NoteModel>> getNotesInFolder({required String folderBackendId}) async {
    return _noteBox.values
        .where((note) => note.folderId == folderBackendId)
        .toList();
  }

  // Get failed notes
  static Future<List<NoteModel>> getNotesFailed({required String userId}) async {
    return _noteFailedBox.values
        .where((note) => note.userId == userId && note.backendNoteId == '')
        .toList();
  }

  // Save all folders (used for sync)
  static Future<void> saveAllFolders({required List<FolderModel> folders}) async {
    // Clear existing folders
    await _folderBox.clear();

    // Flatten nested structure from API
    final flatFolders = _flattenFolderTree(folders);
    debugPrint('HiveFolderService: Flattened ${flatFolders.length} folders from ${folders.length} root folders');

    // Build hierarchy and save
    final processedFolders = _buildFolderHierarchy(flatFolders);
    final folderMap = <String, FolderModel>{};

    for (final folder in processedFolders) {
      folderMap[folder.backendId] = folder;
      debugPrint('HiveFolderService: Saving folder ${folder.folderName} (${folder.backendId}) - parent: ${folder.parentFolderId}');
    }

    await _folderBox.putAll(folderMap);
  }

  // Search folders by name
  static List<FolderModel> searchFolders(String query) {
    if (query.isEmpty) return getAllFolders();
    
    final lowerQuery = query.toLowerCase();
    return _folderBox.values
        .where((folder) => folder.folderName.toLowerCase().contains(lowerQuery))
        .toList();
  }

  // Get folders at specific level
  static List<FolderModel> getFoldersAtLevel(int level) {
    return _folderBox.values
        .where((folder) => folder.level == level)
        .toList();
  }

  // Get folder tree structure
  static List<FolderModel> getFolderTree() {
    final rootFolders = getRootFolders();
    
    // Build tree structure for each root folder
    for (final rootFolder in rootFolders) {
      _buildFolderTreeRecursive(rootFolder);
    }
    
    return rootFolders;
  }

  // Get folder statistics
  static Map<String, dynamic> getFolderStatistics() {
    final allFolders = getAllFolders();
    final rootFolders = getRootFolders();
    
    int maxDepth = 0;
    final levelCounts = <int, int>{};
    int totalNotes = 0;

    for (final folder in allFolders) {
      maxDepth = math.max(maxDepth, folder.level);
      levelCounts[folder.level] = (levelCounts[folder.level] ?? 0) + 1;
      
      // Count notes in folder
      final notesCount = _noteBox.values.where((note) => note.folderId == folder.backendId).length;
      totalNotes += notesCount;
    }

    return {
      'totalFolders': allFolders.length,
      'rootFolders': rootFolders.length,
      'maxDepth': maxDepth,
      'levelCounts': levelCounts,
      'totalNotes': totalNotes,
      'averageNotesPerFolder': allFolders.isNotEmpty ? totalNotes / allFolders.length : 0,
    };
  }

  // Fix data consistency issues
  static Future<void> fixDataConsistency() async {
    final allFolders = _folderBox.values.toList();
    
    for (final folder in allFolders) {
      bool needsUpdate = false;
      var updatedFolder = folder;

      // Fix level
      final correctLevel = _calculateCorrectLevel(folder.backendId);
      if (folder.level != correctLevel) {
        updatedFolder = updatedFolder.copyWith(level: correctLevel);
        needsUpdate = true;
      }

      // Fix path
      final correctPath = _buildPath(folder.folderName, folder.parentFolderId);
      if (folder.path != correctPath) {
        updatedFolder = updatedFolder.copyWith(path: correctPath);
        needsUpdate = true;
      }

      // Validate parent exists
      if (folder.parentFolderId != null && folder.parentFolderId!.isNotEmpty) {
        final parent = _folderBox.get(folder.parentFolderId!);
        if (parent == null) {
          // Remove invalid parent reference
          updatedFolder = updatedFolder.copyWith(
            parentFolderId: null,
            level: 1,
            path: folder.folderName,
          );
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        await _folderBox.put(folder.backendId, updatedFolder);
      }
    }
  }

  // Print folder tree for debugging
  static String printFolderTree() {
    final buffer = StringBuffer();
    final rootFolders = getRootFolders()..sort((a, b) => a.folderName.compareTo(b.folderName));
    
    buffer.writeln('📂 Folder Structure:');
    buffer.writeln('═' * 50);
    
    for (int i = 0; i < rootFolders.length; i++) {
      buffer.write(_printFolderRecursive(rootFolders[i]));
      if (i < rootFolders.length - 1) {
        buffer.writeln();
      }
    }
    
    return buffer.toString();
  }

  // Private helper methods
  static String _buildPath(String folderName, String? parentFolderId) {
    if (parentFolderId == null || parentFolderId.isEmpty) {
      return folderName;
    }
    
    final parent = _folderBox.get(parentFolderId);
    if (parent == null) return folderName;
    
    return '${parent.path}/$folderName';
  }

  static int _calculateCorrectLevel(String folderId) {
    final folder = _folderBox.get(folderId);
    if (folder == null) return 1;
    
    if (folder.parentFolderId == null || folder.parentFolderId!.isEmpty) {
      return 1;
    }
    
    return _calculateCorrectLevel(folder.parentFolderId!) + 1;
  }

  static Future<void> _addSubfolderToParent(String parentId, String subfolderId) async {
    final parent = _folderBox.get(parentId);
    if (parent == null) return;

    final currentSubfolders = getSubFolders(folderBackendId: parentId);
    if (!currentSubfolders.any((f) => f.backendId == subfolderId)) {
      // Parent-child relationship is maintained by parentFolderId, no need for subFolderIds
      // This is automatically handled by the getSubFolders method
    }
  }

  static Future<void> _removeSubfolderFromParent(String parentId, String subfolderId) async {
    // Parent-child relationship is maintained by parentFolderId
    // When we update the child's parentFolderId, the relationship is automatically updated
  }

  static List<FolderModel> _getAllDescendantFolders(String folderId) {
    final descendants = <FolderModel>[];
    final directChildren = getSubFolders(folderBackendId: folderId);
    
    for (final child in directChildren) {
      descendants.add(child);
      descendants.addAll(_getAllDescendantFolders(child.backendId));
    }
    
    return descendants;
  }

  static bool _isDescendantOf(String potentialDescendant, String ancestorId) {
    final folder = _folderBox.get(potentialDescendant);
    if (folder == null || folder.parentFolderId == null) return false;
    
    if (folder.parentFolderId == ancestorId) return true;
    
    return _isDescendantOf(folder.parentFolderId!, ancestorId);
  }

  static Future<void> _updateFolderPath(FolderModel folder) async {
    final newPath = _buildPath(folder.folderName, folder.parentFolderId);
    final updatedFolder = folder.copyWith(path: newPath);
    await _folderBox.put(folder.backendId, updatedFolder);
  }

  static Future<void> _updateDescendantPaths(String folderId) async {
    final descendants = _getAllDescendantFolders(folderId);
    
    for (final descendant in descendants) {
      await _updateFolderPath(descendant);
    }
  }

  /// Flatten nested folder tree from API into flat list
  static List<FolderModel> _flattenFolderTree(List<FolderModel> folders) {
    final flatList = <FolderModel>[];

    void flattenRecursive(FolderModel folder) {
      // Add current folder (without nested subfolders to avoid circular references)
      final flatFolder = folder.copyWith(subfolders: []);
      flatList.add(flatFolder);

      // Recursively add all subfolders
      for (final subfolder in folder.subfolders) {
        flattenRecursive(subfolder);
      }
    }

    // Process all root folders
    for (final folder in folders) {
      flattenRecursive(folder);
    }

    return flatList;
  }

  static List<FolderModel> _buildFolderHierarchy(List<FolderModel> folders) {
    final processedFolders = <FolderModel>[];

    for (final folder in folders) {
      final level = _calculateLevelFromHierarchy(folder, folders);
      final path = _buildPathFromHierarchy(folder, folders);

      final processedFolder = folder.copyWith(
        level: level,
        path: path,
        subfolders: [], // Clear subfolders as they will be built dynamically
      );

      processedFolders.add(processedFolder);
    }

    return processedFolders;
  }

  static int _calculateLevelFromHierarchy(FolderModel folder, List<FolderModel> allFolders) {
    if (folder.parentFolderId == null || folder.parentFolderId!.isEmpty) {
      return 1;
    }
    
    final parent = allFolders.firstWhere(
      (f) => f.backendId == folder.parentFolderId,
      orElse: () => FolderModel(folderName: '', backendId: ''),
    );
    
    if (parent.backendId.isEmpty) return 1;
    
    return _calculateLevelFromHierarchy(parent, allFolders) + 1;
  }

  static String _buildPathFromHierarchy(FolderModel folder, List<FolderModel> allFolders) {
    if (folder.parentFolderId == null || folder.parentFolderId!.isEmpty) {
      return folder.folderName;
    }
    
    final parent = allFolders.firstWhere(
      (f) => f.backendId == folder.parentFolderId,
      orElse: () => FolderModel(folderName: '', backendId: ''),
    );
    
    if (parent.backendId.isEmpty) return folder.folderName;
    
    final parentPath = _buildPathFromHierarchy(parent, allFolders);
    return '$parentPath/${folder.folderName}';
  }

  static void _buildFolderTreeRecursive(FolderModel folder) {
    final subfolders = getSubFolders(folderBackendId: folder.backendId);
    folder.subfolders.clear();
    folder.subfolders.addAll(subfolders);
    
    for (final subfolder in subfolders) {
      _buildFolderTreeRecursive(subfolder);
    }
  }

  static String _printFolderRecursive(FolderModel folder, {String indent = ''}) {
    final buffer = StringBuffer();
    final notesCount = _noteBox.values.where((note) => note.folderId == folder.backendId).length;
    
    buffer.writeln('$indent📁 ${folder.folderName} (Level ${folder.level}, Notes: $notesCount)');
    
    final subfolders = getSubFolders(folderBackendId: folder.backendId);
    for (int i = 0; i < subfolders.length; i++) {
      final isLast = i == subfolders.length - 1;
      final childIndent = indent + (isLast ? '    ' : '│   ');
      final connector = isLast ? '└── ' : '├── ';
      
      buffer.write('$indent$connector');
      buffer.write(_printFolderRecursive(subfolders[i], indent: childIndent)
          .substring(indent.length + connector.length));
    }
    
    return buffer.toString();
  }
}
