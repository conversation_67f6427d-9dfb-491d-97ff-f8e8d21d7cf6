import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class MoveToFolderHelper {
  /// Check if the target folder is invalid for moving
  static bool _isInvalidTarget(MoveFolderViewModel targetFolder, List<String>? foldersToBeMovedIds) {
    if (foldersToBeMovedIds == null || foldersToBeMovedIds.isEmpty) {
      return false; // No folders to move, so any target is valid
    }

    // Check if target is one of the folders being moved
    if (foldersToBeMovedIds.contains(targetFolder.backendId)) {
      return true;
    }

    // Check if target is a descendant of any folder being moved
    for (final folderId in foldersToBeMovedIds) {
      if (_isDescendantOf(targetFolder.backendId, folderId)) {
        return true;
      }
    }

    return false;
  }

  /// Check if a folder is a descendant of another folder
  static bool _isDescendantOf(String potentialDescendant, String ancestorId) {
    final allFolders = HiveFolderService.getAllFolders();
    final folderMap = <String, FolderModel>{};
    for (final folder in allFolders) {
      folderMap[folder.backendId] = folder;
    }

    final folder = folderMap[potentialDescendant];
    if (folder == null || folder.parentFolderId == null) return false;

    if (folder.parentFolderId == ancestorId) return true;

    return _isDescendantOf(folder.parentFolderId!, ancestorId);
  }

  static void showBottomSheet(
    BuildContext context, {
    List<String>? foldersToBeMovedIds,
    String? jumpToFolderId,
    List<NoteModel>? notesToBeMoved,
    VoidCallback? onFolderMoved,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) {
        return BlocProvider(
          create: (context) {
            final cubit = GetIt.instance.get<MoveFolderCubit>();
            cubit.testFolderExpansion(jumpToFolderId);
            cubit.initialize(jumpToFolderId: jumpToFolderId);
            return cubit;
          },
          child: BlocConsumer<MoveFolderCubit, MoveFolderState>(
            listener: (context, state) {
              if (state.oneShotEvent == MoveFolderOneShotEvent.folderMoved) {
                Navigator.pop(context);
                // Show success message
                CommonDialogs.showToast(
                  'Folder moved successfully',
                  gravity: ToastGravity.BOTTOM,
                  length: Toast.LENGTH_SHORT,
                );
                // Notify parent page
                onFolderMoved?.call();
              } else if (state.oneShotEvent == MoveFolderOneShotEvent.subfolderCreated) {
                Navigator.pop(context);
              } else if (state.oneShotEvent == MoveFolderOneShotEvent.error) {
                // Show error message to user
                debugPrint('MoveFolderCubit error: ${state.errorMessage}');
                String errorMessage = state.errorMessage ?? 'Unknown error';

                // Parse specific error messages for better user experience
                if (errorMessage.contains('Cannot move folder into itself')) {
                  errorMessage = 'Cannot move folder into itself';
                } else if (errorMessage.contains('Cannot move folder into its own subfolder')) {
                  errorMessage = 'Cannot move folder into its own subfolder';
                } else if (errorMessage.contains('404')) {
                  errorMessage = 'Folder not found or operation not allowed';
                } else if (errorMessage.contains('Failed to move folder')) {
                  errorMessage = 'Failed to move folder. Please try again.';
                }

                CommonDialogs.showToast(
                  errorMessage,
                  gravity: ToastGravity.BOTTOM,
                  length: Toast.LENGTH_LONG,
                );
              }
            },
            builder: (context, state) {
              if (state.isLoading) {
                return const SafeArea(
                  child: SizedBox(
                    height: 200,
                    child: Center(child: CircularProgressIndicator()),
                  ),
                );
              }

                return StatefulBuilder(
                  builder: (context, setState) {
                    return SafeArea(
                      child: Container(
                        padding: EdgeInsets.only(
                          left: context.isTablet ? 16 : 16.w,
                          right: context.isTablet ? 16 : 16.w,
                          top: context.isTablet ? 16 : 16.h,
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        height: MediaQuery.of(context).size.height * 0.95,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Header
                            Row(
                              children: [
                                Expanded(
                                  child: CommonText(
                                    S.current.add_folder,
                                    style: TextStyle(
                                      fontSize: context.isTablet ? 22 : 20.sp,
                                      fontWeight: FontWeight.w600,
                                      color: context.colorScheme.mainPrimary,
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                    onTap: () => Navigator.pop(context),
                                    child: SvgPicture.asset(
                                      Assets.icons.icCloseWhite,
                                      width: context.isTablet ? 32 : 24.w,
                                      height: context.isTablet ? 32 : 24.w,
                                      fit: BoxFit.contain,
                                      colorFilter: ColorFilter.mode(
                                        context.colorScheme.mainPrimary,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                            // Folder list
                            Expanded(
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  ...state.rootFolders.map((folder) => MoveFolderTile(
                                        folder: folder,
                                        foldersToBeMovedIds: foldersToBeMovedIds,
                                        onTap: (f) {
                                          if (!_isInvalidTarget(f, foldersToBeMovedIds)) {
                                            context.read<MoveFolderCubit>().selectFolder(f);
                                          } else {
                                            // Show toast for invalid selection
                                            CommonDialogs.showToast(
                                              'Cannot move folder into itself or its subfolder',
                                              gravity: ToastGravity.BOTTOM,
                                              length: Toast.LENGTH_SHORT,
                                            );
                                          }
                                        },
                                        onToggleExpansion: (folderId) {
                                          context.read<MoveFolderCubit>().toggleFolderExpansion(folderId);
                                        },
                                      )),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: AppCommonButton(
                                      backgroundColor: context.colorScheme.mainSecondary,
                                      borderRadius: BorderRadius.circular(24.r),
                                      height: context.isTablet ? 44 : 44.h,
                                      onPressed: state.selectedFolder == null
                                          ? null
                                          : () async {
                                              final selectedFolderId = state.selectedFolder!.backendId;
                                              final controller = TextEditingController();
                                              showCreateFolderDialog(
                                                context,
                                                controller: controller,
                                                onPressed: () async {
                                                  try {
                                                    await context
                                                        .read<MoveFolderCubit>()
                                                        .createSubfolder(
                                                          name: controller.text.trim(),
                                                          parentFolderId: selectedFolderId,
                                                        );
                                                    if (context.mounted) {
                                                      Navigator.pop(context);
                                                    }
                                                  } catch (e) {
                                                    debugPrint('Error creating subfolder: $e');
                                                    if (context.mounted) {
                                                      Navigator.pop(context);
                                                    }
                                                  }
                                                },
                                                onClosed: () {
                                                  controller.dispose();
                                                },
                                                title: S.current.create_new_folder,
                                                contentButton: S.current.create,
                                                hintText: S.current.required,
                                                initialValue: '',
                                              );
                                            },
                                      textWidget: CommonText(
                                        S.current.create,
                                        style: TextStyle(
                                          fontSize: context.isTablet ? 18 : 16.sp,
                                            color: context.colorScheme.mainPrimary,
                                            fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW10,
                                  Expanded(
                                    child: AppCommonButton(
                                      backgroundColor: context.colorScheme.mainBlue,
                                      borderRadius: BorderRadius.circular(24.r),
                                      height: context.isTablet ? 44 : 44.h,
                                      onPressed: state.selectedFolder == null || _isInvalidTarget(state.selectedFolder!, foldersToBeMovedIds)
                                          ? null
                                          : () async {
                                              await context
                                                  .read<MoveFolderCubit>()
                                                  .moveFoldersAndNotes(
                                                folderIds: foldersToBeMovedIds ?? [],
                                                notes: notesToBeMoved ?? [],
                                                targetFolderId: state.selectedFolder!.backendId,
                                              );
                                            },
                                      textWidget: CommonText(
                                        S.current.move,
                                        style: TextStyle(
                                          fontSize: context.isTablet ? 18 : 16.sp,
                                          color: context.colorScheme.mainPrimary,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class MoveFolderTile extends StatelessWidget {
  final MoveFolderViewModel folder;
  final Function(MoveFolderViewModel folder)? onTap;
  final Function(String folderId)? onToggleExpansion;
  final List<String>? foldersToBeMovedIds;
  final int level;

  const MoveFolderTile({
    Key? key,
    required this.folder,
    this.onTap,
    this.onToggleExpansion,
    this.foldersToBeMovedIds,
    this.level = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        final isSelected = state.selectedFolder?.backendId == folder.backendId;
        final isInvalid = MoveToFolderHelper._isInvalidTarget(folder, foldersToBeMovedIds);

        debugPrint('MoveFolderTile: Building ${folder.folderName} - expanded: ${folder.isExpanded}, subfolders: ${folder.subfolders.length}');

        return Column(
          children: [
            ListTile(
              contentPadding: EdgeInsets.only(left: 16.w * level),
              tileColor: isSelected
                  ? context.colorScheme.mainSecondary
                  : isInvalid
                      ? context.colorScheme.mainGray.withOpacity(0.3)
                      : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.r),
              ),
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (folder.subfolders.isNotEmpty)
                    GestureDetector(
                      onTap: () {
                        debugPrint('MoveFolderTile: Toggle expansion for ${folder.folderName} (${folder.backendId}) - current expanded: ${folder.isExpanded}');
                        onToggleExpansion?.call(folder.backendId);
                      },
                      child: SvgPicture.asset(
                        folder.isExpanded
                            ? Assets.icons.icExpandMore
                            : Assets.icons.icExpandLess,
                        width: context.isTablet ? 24 : 16.w,
                        height: context.isTablet ? 24 : 16.w,
                        fit: BoxFit.contain,
                        colorFilter: isInvalid
                            ? ColorFilter.mode(
                                context.colorScheme.mainGray.withOpacity(0.5),
                                BlendMode.srcIn,
                              )
                            : null,
                      ),
                    ),
                  SvgPicture.asset(
                    Assets.icons.icFlipFolderMini,
                    width: context.isTablet ? 48 : 32.w,
                    height: context.isTablet ? 48 : 32.w,
                    fit: BoxFit.contain,
                    colorFilter: isInvalid
                        ? ColorFilter.mode(
                            context.colorScheme.mainGray.withOpacity(0.5),
                            BlendMode.srcIn,
                          )
                        : null,
                  ),
                ],
              ),
              title: Text(
                folder.folderName,
                style: TextStyle(
                  color: isInvalid
                      ? context.colorScheme.mainGray.withOpacity(0.5)
                      : null,
                ),
              ),
              onTap: () {
                onTap?.call(folder);
                if (folder.subfolders.isNotEmpty) {
                  onToggleExpansion?.call(folder.backendId);
                }
              },
            ),
            if (folder.isExpanded)
              ...folder.subfolders.map((sub) => MoveFolderTile(
                    folder: sub,
                    onTap: onTap,
                    onToggleExpansion: onToggleExpansion,
                    foldersToBeMovedIds: foldersToBeMovedIds,
                    level: level + 1,
                  )),
          ],
        );
      },
    );
  }
}
